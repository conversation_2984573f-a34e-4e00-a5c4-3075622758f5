<template>
  <div class="right-side-panel">
    <!-- 模型总调用趋势 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">模型总调用趋势</h3>
        <div class="card-controls">
          <Select
            v-model:value="modelCallPeriod"
            class="dark-select"
            style="width: 120px"
            size="small"
          >
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
        </div>
      </div>

      <div class="card-content">
        <div ref="modelCallChartRef" class="chart-container"></div>
      </div>
    </div>

    <!-- 作业管控情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">作业管控情况统计</h3>
        <div class="card-controls">
          <Select
            v-model:value="workControlPeriod"
            class="dark-select"
            style="width: 120px"
            size="small"
          >
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
          <!-- <Button type="primary" size="small" @click="exportWorkControlData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button> -->
        </div>
      </div>

      <div class="card-content">
        <div ref="workControlChartRef" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { Select, SelectOption, message } from 'ant-design-vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import {
    getModelTotalCallTrend,
    getOperationDailyEventStats,
    calculateDateRangeByMonths,
  } from '@/api/bi/dashboard';
  import type { BiModelTotalCallTrendVo, BiOperationDailyEventStatsVo } from '@/api/bi/model';

  const modelCallPeriod = ref('1');
  const workControlPeriod = ref('1');

  const modelCallChartRef = ref();
  const workControlChartRef = ref();

  const { setOptions: setModelCallOptions } = useECharts(modelCallChartRef, 'dark');
  const { setOptions: setWorkControlOptions } = useECharts(workControlChartRef, 'dark');

  // 数据状态
  const modelCallApiData = ref<BiModelTotalCallTrendVo[]>([]);
  const workControlApiData = ref<BiOperationDailyEventStatsVo[]>([]);
  const modelCallLoading = ref(false);
  const workControlLoading = ref(false);

  // 获取模型总调用趋势数据
  async function fetchModelCallData() {
    try {
      modelCallLoading.value = true;
      const months = parseInt(modelCallPeriod.value);
      const { startTime, endTime } = calculateDateRangeByMonths(months);

      const data = await getModelTotalCallTrend({
        startTime,
        endTime,
      });

      modelCallApiData.value = data || [];
    } catch (error) {
      console.error('获取模型总调用趋势数据失败:', error);
      message.error('获取模型总调用趋势数据失败');
      modelCallApiData.value = [];
    } finally {
      modelCallLoading.value = false;
    }
  }

  // 获取作业管控情况统计数据
  async function fetchWorkControlData() {
    try {
      workControlLoading.value = true;
      const months = parseInt(workControlPeriod.value);
      const { startTime, endTime } = calculateDateRangeByMonths(months);

      const data = await getOperationDailyEventStats({
        startTime,
        endTime,
      });

      workControlApiData.value = data || [];
    } catch (error) {
      console.error('获取作业管控情况统计数据失败:', error);
      message.error('获取作业管控情况统计数据失败');
      workControlApiData.value = [];
    } finally {
      workControlLoading.value = false;
    }
  }

  // 初始化模型调用趋势图表
  function initModelCallChart() {
    if (!modelCallApiData.value.length) {
      return;
    }

    const apiData = modelCallApiData.value;
    const dates = apiData.map((item) => item.daily);
    const successCalls = apiData.map((item) => item.totalSuccess);
    const failureCalls = apiData.map((item) => item.totalFailure);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      legend: {
        data: ['成功次数', '失败次数'],
        top: '5%',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      },
      series: [
        {
          name: '成功次数',
          type: 'line',
          data: successCalls,
          smooth: true,
          lineStyle: {
            color: '#16A085',
            width: 3,
            shadowColor: 'rgba(22, 160, 133, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#16A085' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(22, 160, 133, 0.6)' },
                { offset: 1, color: 'rgba(22, 160, 133, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(22, 160, 133, 0.8)',
            },
          },
        },
        {
          name: '失败次数',
          type: 'line',
          data: failureCalls,
          smooth: true,
          lineStyle: {
            color: '#E74C3C',
            width: 3,
            shadowColor: 'rgba(231, 76, 60, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#E74C3C' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(231, 76, 60, 0.6)' },
                { offset: 1, color: 'rgba(231, 76, 60, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(231, 76, 60, 0.8)',
            },
          },
        },
      ],
    };
    setModelCallOptions(option);
  }

  // 初始化作业管控情况图表
  function initWorkControlChart() {
    if (!workControlApiData.value.length) {
      return;
    }

    const apiData = workControlApiData.value;
    const dates = apiData.map(item => item.daily);
    const operationCount = apiData.map(item => item.total);
    const exceptionCount = apiData.map(item => item.totalExcep);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      legend: {
        data: ['作业次数', '异常数量'],
        top: '5%',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
      },
      yAxis: [
        {
          type: 'value',
          name: '作业次数',
          position: 'left',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
        {
          type: 'value',
          name: '异常数量',
          position: 'right',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
      ],
      series: [
        {
          name: '作业次数',
          type: 'line',
          yAxisIndex: 0,
          data: operationCount,
          smooth: true,
          lineStyle: {
            color: '#1F4E79',
            width: 3,
            shadowColor: 'rgba(31, 78, 121, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#1F4E79' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(31, 78, 121, 0.6)' },
                { offset: 1, color: 'rgba(31, 78, 121, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(31, 78, 121, 0.8)',
            },
          },
        },
        {
          name: '异常数量',
          type: 'line',
          yAxisIndex: 1,
          data: exceptionCount,
          smooth: true,
          lineStyle: {
            color: '#E67E22',
            width: 3,
            shadowColor: 'rgba(230, 126, 34, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#E67E22' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(230, 126, 34, 0.6)' },
                { offset: 1, color: 'rgba(230, 126, 34, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(230, 126, 34, 0.8)',
            },
          },
        },
      ],
    };
    setWorkControlOptions(option);
  }

  // 导出数据
  function exportWorkControlData() {
    message.success('作业管控数据导出成功');
  }

  // 监听时间段变化，重新获取数据
  watch(modelCallPeriod, async () => {
    await fetchModelCallData();
    initModelCallChart();
  });

  watch(workControlPeriod, async () => {
    await fetchWorkControlData();
    initWorkControlChart();
  });

  // 监听数据变化，重新初始化图表
  watch(modelCallApiData, initModelCallChart, { deep: true });
  watch(workControlApiData, initWorkControlChart, { deep: true });

  onMounted(async () => {
    // 初始化时获取数据
    await Promise.all([
      fetchModelCallData(),
      fetchWorkControlData()
    ]);

    // 延迟初始化图表，确保DOM已渲染
    setTimeout(() => {
      initModelCallChart();
      initWorkControlChart();
    }, 100);
  });
</script>

<style scoped>
  @import '../styles/common.scss';

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .card-header {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }

    .card-controls {
      align-self: auto;
    }

    .card-title {
      font-size: 15px;
    }
  }

  .right-side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .panel-card {
    flex: 1;
    overflow: hidden;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
  }

  .card-title {
    margin: 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .card-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    padding: 20px;
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
  }
</style>
