<template>
  <div class="dashboard-container">
    <!-- 背景图片 -->
    <img src="@/assets/images/dashboard/banner.png" alt="" class="dashboard-bg" />

    <div
      class="bg-gradient-to-r from-slate-900 to-transparent absolute inset-y-0 left-0 w-2/3"
    ></div>

    <div
      class="bg-gradient-to-l from-slate-900 to-transparent absolute inset-y-0 right-0 w-2/3"
    ></div>

    <!-- 主要内容 -->
    <div class="dashboard-content h-full overflow-auto">
      <!-- 顶部栏 -->
      <DashboardHeader @toggle-fullscreen="toggleFullscreen" />

      <!-- 主体内容区域 -->
      <div class="dashboard-main">
        <!-- 上半部分：数据可视化区域 -->
        <div class="dashboard-top">
          <!-- 左侧模块 -->
          <div class="dashboard-left">
            <LeftSidePanel />
          </div>

          <!-- 中间核心模块 -->
          <div class="dashboard-center">
            <CenterPanel />
          </div>

          <!-- 右侧模块 -->
          <div class="dashboard-right">
            <RightSidePanel />
          </div>
        </div>

        <!-- 下半部分：AR眼镜实时画面 -->
        <div class="dashboard-bottom">
          <ARGlassesPanel />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import DashboardHeader from './components/DashboardHeader.vue';
  import LeftSidePanel from './components/LeftSidePanel.vue';
  import CenterPanel from './components/CenterPanel.vue';
  import RightSidePanel from './components/RightSidePanel.vue';
  import ARGlassesPanel from './components/ARGlassesPanel.vue';

  const router = useRouter();
  const route = useRoute();
  const isFullscreen = ref(false);

  // 切换全屏
  async function toggleFullscreen() {
    try {
      if (!document.fullscreenElement) {
        // 进入全屏
        await document.documentElement.requestFullscreen();
        // 添加 __full__ 查询参数来隐藏 layout
        await router.replace({
          path: route.path,
          query: { ...route.query, __full__: '1' },
        });
        isFullscreen.value = true;
      } else {
        // 退出全屏
        await document.exitFullscreen();
        // 移除 __full__ 查询参数来显示 layout
        const newQuery = { ...route.query };
        delete newQuery.__full__;
        await router.replace({
          path: route.path,
          query: newQuery,
        });
        isFullscreen.value = false;
      }
    } catch (error) {
      console.warn('全屏切换失败:', error);
    }
  }

  // 监听浏览器全屏状态变化
  function handleFullscreenChange() {
    const isCurrentlyFullscreen = !!document.fullscreenElement;

    if (isCurrentlyFullscreen !== isFullscreen.value) {
      isFullscreen.value = isCurrentlyFullscreen;

      // 同步路由状态
      if (isCurrentlyFullscreen && !route.query.__full__) {
        // 进入全屏但路由没有 __full__ 参数，添加它
        router.push({
          path: route.path,
          query: { ...route.query, __full__: '1' },
        });
      } else if (!isCurrentlyFullscreen && route.query.__full__) {
        // 退出全屏但路由还有 __full__ 参数，移除它
        const newQuery = { ...route.query };
        delete newQuery.__full__;
        router.push({
          path: route.path,
          query: newQuery,
        });
      }
    }
  }

  // 初始化全屏状态
  function initFullscreenState() {
    isFullscreen.value = !!document.fullscreenElement;

    // 如果 URL 中有 __full__ 参数但不在浏览器全屏状态，尝试进入全屏
    if (route.query.__full__ && !document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(() => {
        // 如果无法进入浏览器全屏，至少保持 layout 隐藏状态
        console.warn('无法自动进入浏览器全屏模式');
      });
    }
  }

  onMounted(() => {
    initFullscreenState();
    document.addEventListener('fullscreenchange', handleFullscreenChange);
  });

  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
  });
</script>

<style scoped>
  /* 响应式布局 */
  @media (max-width: 1400px) {
    .dashboard-top {
      grid-template-columns: 1fr 1.5fr 1fr;
    }
  }

  @media (max-width: 1200px) {
    .dashboard-top {
      grid-template: 'left center' auto 'right right' auto / 1fr 1fr;
    }

    .dashboard-left {
      grid-area: left;
    }

    .dashboard-center {
      grid-area: center;
    }

    .dashboard-right {
      grid-area: right;
    }

    .dashboard-bottom {
      height: 320px;
    }
  }

  .dashboard-container {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .dashboard-bg {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .dashboard-content {
    display: flex;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
  }

  .dashboard-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-top: 32px;
  }

  .dashboard-top {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    flex: 1;
    min-height: 0;
    gap: 20px;
  }

  .dashboard-left,
  .dashboard-center,
  .dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .dashboard-bottom {
    flex-shrink: 0;
    height: 300px;
  }
</style>
