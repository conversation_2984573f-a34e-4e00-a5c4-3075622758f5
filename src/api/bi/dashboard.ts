import { securityHttp } from '@/utils/http/axios';
import type {
  BiQueryParams,
  BiEventExceptionStatsVo,
  BiVehicleDailyEventStatsVo,
  BiOperationDailyEventStatsVo,
  BiModelTotalCallTrendVo,
  BiCountDailyVo,
  BiArGlassRealTimeScreenStatsVo,
} from './model';

enum Api {
  // 01 业务异常情况统计
  EventExceptionStats = '/bi/onwer/selectEventExceptionStats',
  // 02 工程车辆检修情况统计
  VehicleDailyEventStats = '/bi/onwer/selectVehicleDailyEventStats',
  // 03 作业管控情况统计
  OperationDailyEventStats = '/bi/onwer/selectOperationDailyEventStats',
  // 04 模型总调用趋势
  ModelTotalCallTrend = '/bi/onwer/selectModelTotalCallTrend',
  // 05 今日指标统计
  DailyStats = '/bi/onwer/selectDailyStats',
  // 06 AR眼镜实时画面统计
  ArGlassRealTimeScreenStats = '/bi/onwer/selectArGlassRealTimeScreenStats',
}

/**
 * 01 业务异常情况统计
 * @param params 查询参数
 * @returns 业务异常情况统计数据
 */
export function getEventExceptionStats(params?: BiQueryParams) {
  return securityHttp.get<BiEventExceptionStatsVo[]>({
    url: Api.EventExceptionStats,
    params,
  });
}

/**
 * 02 工程车辆检修情况统计
 * @param params 查询参数
 * @returns 工程车辆检修情况统计数据
 */
export function getVehicleDailyEventStats(params?: BiQueryParams) {
  return securityHttp.get<BiVehicleDailyEventStatsVo[]>({
    url: Api.VehicleDailyEventStats,
    params,
  });
}

/**
 * 03 作业管控情况统计
 * @param params 查询参数
 * @returns 作业管控情况统计数据
 */
export function getOperationDailyEventStats(params?: BiQueryParams) {
  return securityHttp.get<BiOperationDailyEventStatsVo[]>({
    url: Api.OperationDailyEventStats,
    params,
  });
}

/**
 * 04 模型总调用趋势
 * @param params 查询参数
 * @returns 模型总调用趋势数据
 */
export function getModelTotalCallTrend(params?: BiQueryParams) {
  return securityHttp.get<BiModelTotalCallTrendVo[]>({
    url: Api.ModelTotalCallTrend,
    params,
  });
}

/**
 * 05 今日指标统计
 * @returns 今日指标统计数据
 */
export function getDailyStats() {
  return securityHttp.get<BiCountDailyVo>({
    url: Api.DailyStats,
  });
}

/**
 * 06 AR眼镜实时画面统计
 * @param params 查询参数
 * @returns AR眼镜实时画面统计数据
 */
export function getArGlassRealTimeScreenStats(params?: BiQueryParams) {
  return securityHttp.get<BiArGlassRealTimeScreenStatsVo[]>({
    url: Api.ArGlassRealTimeScreenStats,
    params,
  });
}

/**
 * 工具函数：根据天数计算开始和结束时间
 * @param days 天数
 * @returns 开始和结束时间
 */
export function calculateDateRange(days: number): { startTime: string; endTime: string } {
  const now = new Date();
  const endTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;

  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() - days + 1);
  const startTime = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;

  return { startTime, endTime };
}

/**
 * 工具函数：根据月数计算开始和结束时间
 * @param months 月数
 * @returns 开始和结束时间
 */
export function calculateDateRangeByMonths(months: number): { startTime: string; endTime: string } {
  const now = new Date();
  const endTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;

  const startDate = new Date(now);
  startDate.setMonth(startDate.getMonth() - months + 1);
  startDate.setDate(1); // 设置为月初
  const startTime = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;

  return { startTime, endTime };
}

// 保持向后兼容的别名函数
export const calculateMonthRange = calculateDateRange;
export const calculateMonthRangeByMonths = calculateDateRangeByMonths;
